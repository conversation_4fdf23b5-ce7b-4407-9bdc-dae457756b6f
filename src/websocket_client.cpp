// Windows header order fix - must be before any other Windows headers
#if defined(_WIN32)
#ifndef NOMINMAX
#define NOMINMAX
#endif
#ifndef WIN32_LEAN_AND_MEAN
#define WIN32_LEAN_AND_MEAN
#endif
#include <winsock2.h>
#include <ws2tcpip.h>
#include <windows.h>
#endif

#include "websocket_client.h"
#include <iostream>
#include <thread>
#include <chrono>
#include <cstring>
#include <future>

// Static protocol definition
const char* WebSocketClient::protocolName_ = "piano-protocol";

struct lws_protocols WebSocketClient::protocols_[] = {
    {
        WebSocketClient::protocolName_,
        WebSocketClient::lwsCallback,
        0,
        4096,
        0, nullptr, 0
    },
    { nullptr, nullptr, 0, 0, 0, nullptr, 0 } // terminator
};

WebSocketClient::WebSocketClient()
    : connected_(false)
    , shouldStop_(false)
    , disconnectHandled_(false)
    , context_(nullptr)
    , wsi_(nullptr)
    , lastSendTime_(std::chrono::steady_clock::now())
    , autoReconnect_(false)
    , lastPort_(0)
    , reconnectAttempts_(0)
{
}

WebSocketClient::~WebSocketClient() {
    std::cerr << "WebSocketClient destructor called" << std::endl;

    // Force stop all operations
    {
        std::lock_guard<std::mutex> lock(stateMutex_);
        shouldStop_ = true;
        autoReconnect_ = false;
        connected_ = false;
    }

    // Wake up all waiting threads
    messageCondition_.notify_all();

    // Force disconnect with timeout
    disconnect();

    // Wait for reconnect thread to finish with timeout
    if (reconnectThread_ && reconnectThread_->joinable()) {
        std::cerr << "Waiting for reconnect thread to finish..." << std::endl;

        // Try to join with timeout
        auto future = std::async(std::launch::async, [this]() {
            reconnectThread_->join();
        });

        if (future.wait_for(std::chrono::seconds(2)) == std::future_status::timeout) {
            std::cerr << "Reconnect thread join timed out, detaching..." << std::endl;
            reconnectThread_->detach();
        }
        reconnectThread_.reset();
    }

    std::cerr << "WebSocketClient destructor completed" << std::endl;
}

bool WebSocketClient::connect(const std::string& host, int port) {
    std::cerr << "WebSocketClient::connect() called with host=" << host << ", port=" << port << std::endl;

    // Update GUI status for initial connection attempt
    if (errorCallback_) {
        errorCallback_("Connecting to " + host + ":" + std::to_string(port) + "...");
    }

    // Store connection info for auto-reconnect
    lastHost_ = host;
    lastPort_ = port;
    reconnectAttempts_ = 0;

    // Always ensure clean state before connecting
    std::cerr << "Ensuring clean state before connecting..." << std::endl;

    // Set stop flag to ensure any existing operations stop
    {
        std::lock_guard<std::mutex> lock(stateMutex_);
        shouldStop_ = true;
        connected_ = false;
    }

    // Wake up any waiting threads
    messageCondition_.notify_all();

    // Clean up any existing resources with timeout
    if (serviceThread_) {
        if (serviceThread_->joinable()) {
            std::cerr << "Cleaning up existing service thread..." << std::endl;

            // Try to join with timeout
            auto future = std::async(std::launch::async, [this]() {
                serviceThread_->join();
            });

            if (future.wait_for(std::chrono::seconds(1)) == std::future_status::timeout) {
                std::cerr << "Service thread cleanup timed out, detaching..." << std::endl;
                serviceThread_->detach();
            }
        }
        serviceThread_.reset();
    }

    if (messageProcessThread_) {
        if (messageProcessThread_->joinable()) {
            std::cerr << "Cleaning up existing message thread..." << std::endl;

            // Try to join with timeout
            auto future = std::async(std::launch::async, [this]() {
                messageProcessThread_->join();
            });

            if (future.wait_for(std::chrono::seconds(1)) == std::future_status::timeout) {
                std::cerr << "Message thread cleanup timed out, detaching..." << std::endl;
                messageProcessThread_->detach();
            }
        }
        messageProcessThread_.reset();
    }

    if (context_) {
        std::cerr << "Cleaning up existing context..." << std::endl;
        lws_context_destroy(context_);
        context_ = nullptr;
    }

    wsi_ = nullptr;
    std::cerr << "Clean state achieved" << std::endl;

    // Reset flags for new connection
    {
        std::lock_guard<std::mutex> lock(stateMutex_);
        shouldStop_ = false;
        disconnectHandled_ = false;
        std::cerr << "Reset shouldStop_ and disconnectHandled_ flags to false" << std::endl;
    }


    
    // Create LWS context
    struct lws_context_creation_info info;
    memset(&info, 0, sizeof(info));

    info.port = CONTEXT_PORT_NO_LISTEN;
    info.protocols = protocols_;
    info.gid = -1;
    info.uid = -1;
    info.user = this;

    // Set connection timeout to 500ms for very quick response
    info.timeout_secs = 1;  // Minimum is 1 second for libwebsockets
    
    context_ = lws_create_context(&info);
    if (!context_) {
        return false;
    }
    
    // Create connection
    struct lws_client_connect_info ccinfo;
    memset(&ccinfo, 0, sizeof(ccinfo));

    ccinfo.context = context_;
    ccinfo.address = host.c_str();
    ccinfo.port = port;
    ccinfo.path = "/";
    ccinfo.host = ccinfo.address;
    ccinfo.origin = ccinfo.address;
    ccinfo.protocol = protocolName_;
    ccinfo.userdata = this;

    // Set connection timeout to 2 seconds (in milliseconds)
    ccinfo.retry_and_idle_policy = nullptr;  // Use default retry policy
    
    wsi_ = lws_client_connect_via_info(&ccinfo);
    if (!wsi_) {
        lws_context_destroy(context_);
        context_ = nullptr;

        // Update GUI status for connection failure
        if (errorCallback_) {
            errorCallback_("Failed to connect to " + host + ":" + std::to_string(port));
        }

        return false;
    }

    // Start WebSocket service thread (handles network I/O only)
    std::cerr << "Creating service thread..." << std::endl;
    serviceThread_ = std::make_unique<std::thread>([this]() {
        std::cerr << "Service thread started" << std::endl;
        while (!shouldStop_) {
            // Process WebSocket events with very short timeout for maximum responsiveness
            int result = lws_service(context_, 10); // 10ms timeout for very quick response

            // If service returns non-zero, there might be an error
            if (result < 0) {
                std::cerr << "lws_service returned error: " << result << std::endl;
                break;
            }

            // Small sleep to prevent excessive CPU usage while staying very responsive
            std::this_thread::sleep_for(std::chrono::milliseconds(1));
        }
        std::cerr << "Service thread ending" << std::endl;
    });

    // Start message processing thread (handles incoming message processing)
    std::cerr << "Creating message processing thread..." << std::endl;
    messageProcessThread_ = std::make_unique<std::thread>([this]() {
        std::cerr << "Message processing thread started" << std::endl;
        while (!shouldStop_) {
            std::unique_lock<std::mutex> lock(messageMutex_);

            // Wait for messages or stop signal
            messageCondition_.wait(lock, [this] {
                return !incomingMessages_.empty() || shouldStop_;
            });

            // Process all available messages
            while (!incomingMessages_.empty() && !shouldStop_) {
                std::string message = incomingMessages_.front();
                incomingMessages_.pop();

                // Release lock while processing message
                lock.unlock();
                handleMessage(message);
                lock.lock();
            }
        }
        std::cerr << "Message processing thread ending" << std::endl;
    });

    std::cerr << "WebSocketClient::connect() completed successfully" << std::endl;
    return true;
}

void WebSocketClient::disconnect() {
    std::cerr << "WebSocketClient::disconnect() called" << std::endl;

    // Update GUI status for manual disconnection
    if (errorCallback_) {
        errorCallback_("Disconnecting...");
    }

    // Check if already disconnected
    bool alreadyDisconnected = false;
    {
        std::lock_guard<std::mutex> lock(stateMutex_);
        alreadyDisconnected = shouldStop_ && !connected_ && !context_ && !wsi_;

        // Set stop flag first to prevent new operations
        shouldStop_ = true;
        connected_ = false;
        // Disable auto-reconnect for manual disconnection
        autoReconnect_ = false;
        std::cerr << "Set shouldStop_ = true, connected_ = false, autoReconnect_ = false" << std::endl;
    }

    if (alreadyDisconnected) {
        std::cerr << "Already disconnected, skipping cleanup" << std::endl;
        return;
    }

    // Wake up message processing thread first
    std::cerr << "Notifying message processing thread..." << std::endl;
    messageCondition_.notify_all();

    // Close WebSocket connection properly (only if still valid)
    bool hasValidConnection = false;
    {
        std::lock_guard<std::mutex> lock(stateMutex_);
        hasValidConnection = (wsi_ != nullptr && context_ != nullptr);
        if (hasValidConnection) {
            std::cerr << "Requesting connection close..." << std::endl;
            // Request connection close
            lws_close_reason(wsi_, LWS_CLOSE_STATUS_NORMAL, nullptr, 0);
            // Don't set wsi_ to nullptr here - let the callback handle it
        } else {
            std::cerr << "Connection already closed, skipping close request" << std::endl;
            wsi_ = nullptr; // Ensure it's null
        }
    }

    // Wake up service thread to process the close (only if context is valid)
    if (hasValidConnection && context_) {
        std::cerr << "Cancelling service..." << std::endl;
        lws_cancel_service(context_);

        // Give the service thread a moment to process the close
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }

    // Perform cleanup
    cleanupAfterDisconnect(true); // true = set shouldStop_

    // Update GUI status for completed disconnection
    if (errorCallback_) {
        errorCallback_("Disconnected");
    }

    std::cerr << "WebSocketClient::disconnect() completed" << std::endl;
}

bool WebSocketClient::isConnected() const {
    return connected_;
}

bool WebSocketClient::sendMidi(const std::vector<MidiBuffer>& buffers) {
    if (!connected_ || buffers.empty()) {
        return false;
    }

    // Limit the number of buffers per message to prevent large messages
    // Increased from 10 to 50 for better chord/simultaneous note handling
    const size_t maxBuffersPerMessage = 50;

    // Split large buffer arrays into smaller chunks
    for (size_t i = 0; i < buffers.size(); i += maxBuffersPerMessage) {
        size_t end = std::min(i + maxBuffersPerMessage, buffers.size());
        std::vector<MidiBuffer> chunk(buffers.begin() + i, buffers.begin() + end);

        nlohmann::json message;
        message["type"] = "midi";

        // Convert MidiBuffer structs to arrays
        nlohmann::json buffersArray = nlohmann::json::array();
        for (const auto& buffer : chunk) {
            nlohmann::json bufferArray = nlohmann::json::array();
            bufferArray.push_back(buffer.status);
            bufferArray.push_back(buffer.note);
            bufferArray.push_back(buffer.velocity);
            bufferArray.push_back(buffer.color_r);
            bufferArray.push_back(buffer.color_g);
            bufferArray.push_back(buffer.color_b);
            bufferArray.push_back(buffer.delta_hi);
            bufferArray.push_back(buffer.delta_lo);
            buffersArray.push_back(bufferArray);
        }

        message["buffers"] = buffersArray;

        // Queue each chunk separately
        queueMessage(message);
    }

    return true;
}

bool WebSocketClient::sendMidi(const MidiBuffer& buffer) {
    return sendMidi(std::vector<MidiBuffer>{buffer});
}

bool WebSocketClient::sendNoteOn(uint8_t note, uint8_t velocity, uint32_t color, uint16_t deltaTime) {
    MidiBuffer buffer;
    buffer.status = 144; // Note On
    buffer.note = note;
    buffer.velocity = velocity;
    buffer.setColor(color);
    buffer.setDeltaTime(deltaTime);
    
    return sendMidi(buffer);
}

bool WebSocketClient::sendNoteOff(uint8_t note, uint8_t velocity, uint32_t color, uint16_t deltaTime) {
    MidiBuffer buffer;
    buffer.status = 128; // Note Off
    buffer.note = note;
    buffer.velocity = velocity;
    buffer.setColor(color);
    buffer.setDeltaTime(deltaTime);
    
    return sendMidi(buffer);
}

bool WebSocketClient::updateUsername(const std::string& username) {
    if (!connected_) {
        return false;
    }

    nlohmann::json message;
    message["type"] = "set_username";
    message["username"] = username;

    // Update local user info
    currentUser_.username = username;

    return sendMessage(message);
}

bool WebSocketClient::requestUsers() {
    if (!connected_) {
        return false;
    }
    
    nlohmann::json message;
    message["type"] = "get_users";
    
    return sendMessage(message);
}

bool WebSocketClient::sendPing() {
    if (!connected_) {
        return false;
    }

    nlohmann::json message;
    message["type"] = "ping";

    return sendMessage(message);
}

bool WebSocketClient::sendChat(const std::string& message) {
    if (!connected_) {
        return false;
    }

    nlohmann::json chatMessage;
    chatMessage["type"] = "chat";
    chatMessage["message"] = message;

    return sendMessage(chatMessage);
}

// Callback setters
void WebSocketClient::setConnectedCallback(ConnectedCallback callback) {
    connectedCallback_ = callback;
}

void WebSocketClient::setMidiCallback(MidiCallback callback) {
    midiCallback_ = callback;
}

void WebSocketClient::setUsernameUpdatedCallback(UsernameUpdatedCallback callback) {
    usernameUpdatedCallback_ = callback;
}

void WebSocketClient::setUserUpdateCallback(UserUpdateCallback callback) {
    userUpdateCallback_ = callback;
}

void WebSocketClient::setErrorCallback(ErrorCallback callback) {
    errorCallback_ = callback;
}

void WebSocketClient::setPongCallback(PongCallback callback) {
    pongCallback_ = callback;
}

void WebSocketClient::setChatCallback(ChatCallback callback) {
    chatCallback_ = callback;
}

bool WebSocketClient::sendMessage(const nlohmann::json& message) {
    // Queue the message for thread-safe sending
    queueMessage(message);
    return true;
}

void WebSocketClient::queueMessage(const nlohmann::json& message) {
    std::lock_guard<std::mutex> lock(outgoingMutex_);

    // Limit queue size to prevent memory issues
    // Increased from 1000 to 5000 for better handling of rapid note sequences
    const size_t maxQueueSize = 5000;
    if (outgoingMessages_.size() >= maxQueueSize) {
        // Drop oldest messages if queue is full
        while (outgoingMessages_.size() >= maxQueueSize) {
            outgoingMessages_.pop();
        }
    }

    outgoingMessages_.push(message.dump());

    // Request writable callback to process the queue
    if (connected_ && wsi_) {
        lws_callback_on_writable(wsi_);
        // Don't call lws_cancel_service here as it causes excessive callbacks
        // The service thread will pick up the writable callback naturally
    }
}

bool WebSocketClient::sendMessageDirect(const std::string& messageStr) {
    if (!connected_ || !wsi_) {
        return false;
    }

    size_t len = messageStr.length();

    // Check if message is too large
    if (len > 32768) { // 32KB limit
        // Split large messages or reject them
        return false;
    }

    // Allocate buffer with LWS_PRE padding
    std::vector<unsigned char> buffer(LWS_PRE + len);
    memcpy(&buffer[LWS_PRE], messageStr.c_str(), len);

    int result = lws_write(wsi_, &buffer[LWS_PRE], len, LWS_WRITE_TEXT);

    return result >= 0;
}



void WebSocketClient::processOutgoingQueue() {
    std::lock_guard<std::mutex> lock(outgoingMutex_);

    auto now = std::chrono::steady_clock::now();
    auto timeSinceLastSend = std::chrono::duration_cast<std::chrono::milliseconds>(now - lastSendTime_);

    // Rate limiting to prevent excessive sending
    if (timeSinceLastSend.count() < 1) { // 1ms minimum between sends
        return;
    }

    // Process messages in small batches
    int processed = 0;
    const int maxPerCall = 3; // Smaller batch size to reduce callback frequency

    while (!outgoingMessages_.empty() && processed < maxPerCall) {
        std::string message = outgoingMessages_.front();
        outgoingMessages_.pop();

        // Release lock temporarily to send message
        outgoingMutex_.unlock();
        bool success = sendMessageDirect(message);
        outgoingMutex_.lock();

        if (success) {
            lastSendTime_ = std::chrono::steady_clock::now();
            processed++;
        } else {
            // If send failed, put message back at front of queue
            std::queue<std::string> temp;
            temp.push(message);
            while (!outgoingMessages_.empty()) {
                temp.push(outgoingMessages_.front());
                outgoingMessages_.pop();
            }
            outgoingMessages_ = temp;
            break;
        }
    }
}

void WebSocketClient::handleMessage(const std::string& message) {
    // Check if we should stop processing (client is being destroyed)
    {
        std::lock_guard<std::mutex> lock(stateMutex_);
        if (shouldStop_) {
            return;
        }
    }

    try {
        nlohmann::json data = nlohmann::json::parse(message);

        if (!data.contains("type")) {
            return;
        }

        std::string type = data["type"];

        if (type == "connected") {
            if (data.contains("clientId") && data.contains("user")) {
                clientId_ = data["clientId"];

                auto userObj = data["user"];
                currentUser_.id = userObj["id"];
                currentUser_.username = userObj["username"];

                // Parse active users
                activeUsers_.clear();
                if (data.contains("activeUsers")) {
                    for (const auto& userJson : data["activeUsers"]) {
                        UserInfo user;
                        user.id = userJson["id"];
                        user.username = userJson["username"];
                        user.connectedClients = userJson.value("connectedClients", 1);
                        activeUsers_.push_back(user);
                    }
                }

                {
                    std::lock_guard<std::mutex> lock(stateMutex_);
                    if (connectedCallback_ && !shouldStop_) {
                        connectedCallback_(clientId_, currentUser_, activeUsers_);
                    }
                }
            }
        }
        else if (type == "midi") {
            if (data.contains("buffers") && data.contains("fromUser")) {
                std::vector<MidiBuffer> buffers;

                for (const auto& bufferJson : data["buffers"]) {
                    if (bufferJson.size() >= 8) {
                        MidiBuffer buffer;
                        buffer.status = bufferJson[0];
                        buffer.note = bufferJson[1];
                        buffer.velocity = bufferJson[2];
                        buffer.color_r = bufferJson[3];
                        buffer.color_g = bufferJson[4];
                        buffer.color_b = bufferJson[5];
                        buffer.delta_hi = bufferJson[6];
                        buffer.delta_lo = bufferJson[7];
                        buffers.push_back(buffer);
                    }
                }

                UserInfo fromUser;
                auto fromUserObj = data["fromUser"];
                fromUser.id = fromUserObj["id"];
                fromUser.username = fromUserObj["username"];

                {
                    std::lock_guard<std::mutex> lock(stateMutex_);
                    if (midiCallback_ && !buffers.empty() && !shouldStop_) {
                        midiCallback_(buffers, fromUser);
                    }
                }
            }
        }
        else if (type == "username_updated") {
            if (data.contains("username")) {
                std::string newUsername = data["username"];
                currentUser_.username = newUsername;

                if (usernameUpdatedCallback_ && !shouldStop_) {
                    usernameUpdatedCallback_(newUsername);
                }
            }
        }
        else if (type == "user_update") {
            if (data.contains("users")) {
                activeUsers_.clear();
                for (const auto& userJson : data["users"]) {
                    UserInfo user;
                    user.id = userJson["id"];
                    user.username = userJson["username"];
                    user.connectedClients = userJson.value("connectedClients", 1);
                    activeUsers_.push_back(user);
                }

                if (userUpdateCallback_ && !shouldStop_) {
                    userUpdateCallback_(activeUsers_);
                }
            }
        }
        else if (type == "error") {
            if (data.contains("message")) {
                std::string errorMessage = data["message"];

                if (errorCallback_ && !shouldStop_) {
                    errorCallback_(errorMessage);
                }
            }
        }
        else if (type == "pong") {
            if (pongCallback_ && !shouldStop_) {
                pongCallback_();
            }
        }
        else if (type == "chat") {
            if (data.contains("message") && data.contains("fromUser") && data.contains("timestamp")) {
                std::string message = data["message"];
                uint64_t timestamp = data["timestamp"];

                auto userObj = data["fromUser"];
                UserInfo fromUser;
                fromUser.id = userObj["id"];
                fromUser.username = userObj["username"];

                if (chatCallback_ && !shouldStop_) {
                    chatCallback_(message, fromUser, timestamp);
                }
            }
        }
        else if (type == "chat_history") {
            if (data.contains("messages")) {
                auto messages = data["messages"];
                for (const auto& msgData : messages) {
                    if (msgData.contains("message") && msgData.contains("fromUser") && msgData.contains("timestamp")) {
                        std::string message = msgData["message"];
                        uint64_t timestamp = msgData["timestamp"];

                        auto userObj = msgData["fromUser"];
                        UserInfo fromUser;
                        fromUser.id = userObj["id"];
                        fromUser.username = userObj["username"];

                        if (chatCallback_ && !shouldStop_) {
                            chatCallback_(message, fromUser, timestamp);
                        }
                    }
                }
            }
        }
    }
    catch (const std::exception& e) {
        if (errorCallback_ && !shouldStop_) {
            errorCallback_("Failed to parse message: " + std::string(e.what()));
        }
    }
}

int WebSocketClient::lwsCallback(struct lws* wsi, enum lws_callback_reasons reason,
                                void* user, void* in, size_t len) {
    // Get context first and check if it's valid
    struct lws_context* context = lws_get_context(wsi);
    if (!context) {
        std::cerr << "lwsCallback: Invalid context" << std::endl;
        return 0;
    }

    WebSocketClient* client = static_cast<WebSocketClient*>(lws_context_user(context));

    // Safety check: ensure client is valid and not being destroyed
    if (!client) {
        std::cerr << "lwsCallback: Invalid client pointer" << std::endl;
        return 0;
    }

    // Additional safety check with mutex
    {
        std::lock_guard<std::mutex> lock(client->stateMutex_);
        if (client->shouldStop_) {
            std::cerr << "lwsCallback: Client is stopping, ignoring callback reason " << reason << std::endl;
            return 0;
        }
    }

    switch (reason) {
        case LWS_CALLBACK_CLIENT_ESTABLISHED:
            {
                std::cerr << "lwsCallback: CLIENT_ESTABLISHED" << std::endl;
                std::lock_guard<std::mutex> lock(client->stateMutex_);
                if (!client->shouldStop_) {
                    client->connected_ = true;
                    std::cerr << "lwsCallback: Connection established successfully, connected_=true" << std::endl;
                } else {
                    std::cerr << "lwsCallback: Connection established but shouldStop_=true, not setting connected_" << std::endl;
                }
            }
            break;

        case LWS_CALLBACK_CLIENT_RECEIVE:
            if (client && !client->shouldStop_ && in && len > 0) {
                std::string message(static_cast<char*>(in), len);

                {
                    std::lock_guard<std::mutex> lock(client->messageMutex_);

                    // Don't process messages if stopping
                    if (client->shouldStop_) {
                        break;
                    }

                    // Increased queue size for better buffering
                    const size_t maxIncomingQueueSize = 100;
                    if (client->incomingMessages_.size() >= maxIncomingQueueSize) {
                        // Remove oldest messages if queue is full
                        while (client->incomingMessages_.size() >= maxIncomingQueueSize) {
                            client->incomingMessages_.pop();
                        }
                    }

                    client->incomingMessages_.push(message);
                }

                // Wake up message processing thread immediately
                if (!client->shouldStop_) {
                    client->messageCondition_.notify_one();
                }
            }
            break;

        case LWS_CALLBACK_CLIENT_CONNECTION_ERROR:
            {
                std::cerr << "lwsCallback: CLIENT_CONNECTION_ERROR" << std::endl;
                client->handleConnectionLoss("Connection error");
            }
            break;

        case LWS_CALLBACK_CLOSED:
            {
                std::cerr << "lwsCallback: CLOSED" << std::endl;
                client->handleConnectionLoss("Server disconnected");
            }
            break;

        case LWS_CALLBACK_WSI_DESTROY:
            {
                std::cerr << "lwsCallback: WSI_DESTROY" << std::endl;
                // WSI is being destroyed - just clean up the pointer
                {
                    std::lock_guard<std::mutex> lock(client->stateMutex_);
                    client->wsi_ = nullptr;
                }
            }
            break;

        case LWS_CALLBACK_CLIENT_WRITEABLE:
            // Process outgoing message queue when socket is writable
            if (client && !client->shouldStop_) {
                client->processOutgoingQueue();

                // Request callback again if there are more messages, but with rate limiting
                {
                    std::lock_guard<std::mutex> lock(client->outgoingMutex_);
                    if (!client->outgoingMessages_.empty()) {
                        // Only request writable if we haven't requested recently
                        auto now = std::chrono::steady_clock::now();
                        static auto lastWritableRequest = std::chrono::steady_clock::now();
                        if (std::chrono::duration_cast<std::chrono::milliseconds>(now - lastWritableRequest).count() > 1) {
                            lws_callback_on_writable(wsi);
                            lastWritableRequest = now;
                        }
                    }
                }
            }
            break;

        case LWS_CALLBACK_CLIENT_CLOSED:
            {
                std::cerr << "lwsCallback: CLIENT_CLOSED" << std::endl;
                client->handleConnectionLoss("Connection closed");
            }
            break;

        case LWS_CALLBACK_EVENT_WAIT_CANCELLED:
            // This is normal - just means the event loop was cancelled
            // Don't log this as it happens frequently
            break;

        case LWS_CALLBACK_GET_THREAD_ID:
        case LWS_CALLBACK_LOCK_POLL:
        case LWS_CALLBACK_UNLOCK_POLL:
        case LWS_CALLBACK_ADD_POLL_FD:
        case LWS_CALLBACK_DEL_POLL_FD:
        case LWS_CALLBACK_CHANGE_MODE_POLL_FD:
            // These are internal libwebsockets callbacks, don't log them
            break;

        default:
            // Log only truly unhandled callbacks for debugging
            std::cerr << "lwsCallback: Unhandled callback reason " << reason << std::endl;
            break;
    }

    return 0;
}

// Auto-reconnection methods
void WebSocketClient::setAutoReconnect(bool enable) {
    autoReconnect_ = enable;
}

bool WebSocketClient::getAutoReconnect() const {
    return autoReconnect_;
}

void WebSocketClient::cleanupAfterDisconnect(bool setShouldStop) {
    std::cerr << "cleanupAfterDisconnect() called, setShouldStop=" << setShouldStop << std::endl;

    // Update state
    {
        std::lock_guard<std::mutex> lock(stateMutex_);
        if (setShouldStop) {
            shouldStop_ = true;
        }
        connected_ = false;
        wsi_ = nullptr;
    }

    // Wake up message processing thread
    messageCondition_.notify_all();

    // Wait for threads to finish with timeout
    std::cerr << "Waiting for threads to finish..." << std::endl;
    if (serviceThread_) {
        if (serviceThread_->joinable()) {
            std::cerr << "Joining service thread..." << std::endl;

            // Try to join with timeout
            auto future = std::async(std::launch::async, [this]() {
                serviceThread_->join();
            });

            if (future.wait_for(std::chrono::seconds(2)) == std::future_status::timeout) {
                std::cerr << "Service thread join timed out, detaching..." << std::endl;
                serviceThread_->detach();
            } else {
                std::cerr << "Service thread joined" << std::endl;
            }
        } else {
            std::cerr << "Service thread not joinable" << std::endl;
        }
        serviceThread_.reset();
        std::cerr << "Service thread reset" << std::endl;
    }

    if (messageProcessThread_) {
        if (messageProcessThread_->joinable()) {
            std::cerr << "Joining message process thread..." << std::endl;

            // Try to join with timeout
            auto future = std::async(std::launch::async, [this]() {
                messageProcessThread_->join();
            });

            if (future.wait_for(std::chrono::seconds(2)) == std::future_status::timeout) {
                std::cerr << "Message process thread join timed out, detaching..." << std::endl;
                messageProcessThread_->detach();
            } else {
                std::cerr << "Message process thread joined" << std::endl;
            }
        } else {
            std::cerr << "Message process thread not joinable" << std::endl;
        }
        messageProcessThread_.reset();
        std::cerr << "Message process thread reset" << std::endl;
    }

    // Clean up context
    std::cerr << "Cleaning up context..." << std::endl;
    if (context_) {
        lws_context_destroy(context_);
        context_ = nullptr;
        std::cerr << "Context destroyed and reset" << std::endl;
    }

    // Clear user state
    clientId_.clear();
    currentUser_ = UserInfo{};
    activeUsers_.clear();

    // Clear message queues
    {
        std::lock_guard<std::mutex> lock(messageMutex_);
        while (!incomingMessages_.empty()) {
            incomingMessages_.pop();
        }
    }

    {
        std::lock_guard<std::mutex> lock(outgoingMutex_);
        while (!outgoingMessages_.empty()) {
            outgoingMessages_.pop();
        }
    }

    // Handle reconnect thread (only for manual disconnect)
    if (setShouldStop && reconnectThread_) {
        if (reconnectThread_->joinable()) {
            // Check if we're being called from the reconnect thread itself
            if (reconnectThread_->get_id() == std::this_thread::get_id()) {
                std::cerr << "Called from reconnect thread itself, detaching..." << std::endl;
                reconnectThread_->detach();
            } else {
                std::cerr << "Joining reconnect thread..." << std::endl;

                // Try to join with timeout
                auto future = std::async(std::launch::async, [this]() {
                    reconnectThread_->join();
                });

                if (future.wait_for(std::chrono::seconds(1)) == std::future_status::timeout) {
                    std::cerr << "Reconnect thread join timed out, detaching..." << std::endl;
                    reconnectThread_->detach();
                } else {
                    std::cerr << "Reconnect thread joined successfully" << std::endl;
                }
            }
        }
        reconnectThread_.reset();
        std::cerr << "Reconnect thread reset" << std::endl;
    }

    std::cerr << "cleanupAfterDisconnect() completed" << std::endl;
}

void WebSocketClient::handleConnectionLoss(const std::string& reason) {
    std::cerr << "Connection lost: " << reason << std::endl;

    // Check if this is a manual disconnection or already handled
    bool isManualDisconnect = false;
    bool alreadyHandled = false;
    {
        std::lock_guard<std::mutex> lock(stateMutex_);
        isManualDisconnect = shouldStop_;
        alreadyHandled = disconnectHandled_;

        // Mark as handled to prevent duplicate processing
        if (!alreadyHandled) {
            disconnectHandled_ = true;
        }
    }

    // Don't process if this was a manual disconnect or already handled
    if (isManualDisconnect || alreadyHandled) {
        std::cerr << "Manual disconnection or already handled, skipping reconnection" << std::endl;
        return;
    }

    std::cerr << "Server disconnection detected" << std::endl;

    // Call error callback to update UI
    if (errorCallback_) {
        std::cerr << "Calling error callback for server disconnect" << std::endl;
        errorCallback_(reason);
    }

    // Set connection state immediately
    {
        std::lock_guard<std::mutex> lock(stateMutex_);
        connected_ = false;
        wsi_ = nullptr;
    }

    // Attempt reconnection if enabled
    if (autoReconnect_ && !lastHost_.empty()) {
        std::cerr << "Scheduling auto-reconnection..." << std::endl;

        // Reset flags for reconnection
        {
            std::lock_guard<std::mutex> lock(stateMutex_);
            shouldStop_ = false;
            disconnectHandled_ = false;
        }

        // Start reconnection in a separate detached thread to avoid blocking
        std::thread([this]() {
            std::this_thread::sleep_for(std::chrono::milliseconds(100)); // Brief delay
            attemptReconnect();
        }).detach();
    }
}

void WebSocketClient::attemptReconnect() {
    std::cerr << "attemptReconnect() called" << std::endl;

    // Check if we should abort reconnection
    {
        std::lock_guard<std::mutex> lock(stateMutex_);
        if (shouldStop_) {
            std::cerr << "attemptReconnect: shouldStop_ is true, aborting" << std::endl;
            return;
        }
    }

    if (!autoReconnect_) {
        std::cerr << "attemptReconnect: autoReconnect_ is false, aborting" << std::endl;
        return;
    }

    if (lastHost_.empty()) {
        std::cerr << "attemptReconnect: lastHost_ is empty, aborting" << std::endl;
        return;
    }

    // Check if we've exceeded max attempts
    if (reconnectAttempts_ >= maxReconnectAttempts_) {
        std::cerr << "Max reconnection attempts reached. Giving up." << std::endl;
        autoReconnect_ = false;
        if (errorCallback_) {
            errorCallback_("Connection failed - max " + std::to_string(maxReconnectAttempts_) + " attempts reached");
        }
        return;
    }

    reconnectAttempts_++;

    // Calculate exponential backoff delay: 500ms, 1s, 2s, 4s, 8s, 16s, 32s, 60s, 60s, ...
    int delayMs = std::min(
        static_cast<int>(initialReconnectDelay_.count() * (1 << (reconnectAttempts_ - 1))),
        maxReconnectDelay_ * 1000  // Convert max delay to milliseconds
    );
    int delaySeconds = (delayMs + 999) / 1000;  // Round up to nearest second for display

    std::cerr << "Attempting reconnection " << reconnectAttempts_ << "/" << maxReconnectAttempts_
              << " in " << delayMs << "ms (" << delaySeconds << " seconds)..." << std::endl;

    // Update GUI status for reconnection attempt
    if (errorCallback_) {
        if (delayMs < 1000) {
            errorCallback_("Reconnection attempt " + std::to_string(reconnectAttempts_) + "/" +
                          std::to_string(maxReconnectAttempts_) + " in " + std::to_string(delayMs) + "ms...");
        } else {
            errorCallback_("Reconnection attempt " + std::to_string(reconnectAttempts_) + "/" +
                          std::to_string(maxReconnectAttempts_) + " in " + std::to_string(delaySeconds) + " seconds...");
        }
    }

    // Start reconnection in a separate thread to avoid blocking
    if (reconnectThread_ && reconnectThread_->joinable()) {
        reconnectThread_->join();
    }

    reconnectThread_ = std::make_unique<std::thread>([this, delayMs, delaySeconds]() {
        std::cerr << "Reconnect thread started, delayMs=" << delayMs << std::endl;

        // Wait with status updates
        if (delayMs < 1000) {
            // For delays less than 1 second, show milliseconds and wait without countdown
            if (errorCallback_) {
                errorCallback_("Reconnecting in " + std::to_string(delayMs) + "ms...");
            }

            // Sleep in small chunks to allow for early termination
            int remainingMs = delayMs;
            while (remainingMs > 0) {
                {
                    std::lock_guard<std::mutex> lock(stateMutex_);
                    if (shouldStop_ || !autoReconnect_) {
                        std::cerr << "Reconnect thread stopping early: shouldStop_=" << shouldStop_ << ", autoReconnect_=" << autoReconnect_ << std::endl;
                        return;
                    }
                }

                int sleepMs = std::min(remainingMs, 50);  // Sleep in 50ms chunks
                std::this_thread::sleep_for(std::chrono::milliseconds(sleepMs));
                remainingMs -= sleepMs;
            }
        } else {
            // For delays 1 second or more, show countdown in seconds
            for (int i = delaySeconds; i > 0; i--) {
                {
                    std::lock_guard<std::mutex> lock(stateMutex_);
                    if (shouldStop_ || !autoReconnect_) {
                        std::cerr << "Reconnect thread stopping early: shouldStop_=" << shouldStop_ << ", autoReconnect_=" << autoReconnect_ << std::endl;
                        return;
                    }
                }

                // Update status with countdown
                if (errorCallback_) {
                    errorCallback_("Reconnecting in " + std::to_string(i) + " seconds...");
                }

                // Sleep in smaller chunks to be more responsive to stop signals
                for (int j = 0; j < 10; j++) {
                    {
                        std::lock_guard<std::mutex> lock(stateMutex_);
                        if (shouldStop_ || !autoReconnect_) {
                            return;
                        }
                    }
                    std::this_thread::sleep_for(std::chrono::milliseconds(100));
                }
            }
        }

        std::cerr << "Countdown completed, attempting connection..." << std::endl;

        // Final check before attempting connection
        {
            std::lock_guard<std::mutex> lock(stateMutex_);
            if (shouldStop_ || !autoReconnect_) {
                std::cerr << "Reconnect aborted before connection attempt" << std::endl;
                return;
            }
        }

        // Update status to show connection attempt
        if (errorCallback_) {
            errorCallback_("Connecting to " + lastHost_ + ":" + std::to_string(lastPort_) + "...");
        }

        // Clean up any existing connection state before reconnecting
        if (context_) {
            lws_context_destroy(context_);
            context_ = nullptr;
        }

        bool connectionSuccess = false;
        try {
            connectionSuccess = connect(lastHost_, lastPort_);
        } catch (const std::exception& e) {
            std::cerr << "Exception during reconnection: " << e.what() << std::endl;
            connectionSuccess = false;
        }

        if (connectionSuccess) {
            std::cerr << "Reconnection successful!" << std::endl;
            reconnectAttempts_ = 0; // Reset on successful connection

            // Update GUI status for successful reconnection
            if (errorCallback_) {
                errorCallback_("Reconnected successfully!");
            }
        } else {
            std::cerr << "Reconnection attempt " << reconnectAttempts_ << " failed." << std::endl;

            // Update GUI status for failed reconnection
            if (errorCallback_) {
                if (reconnectAttempts_ >= maxReconnectAttempts_) {
                    errorCallback_("Reconnection failed after " + std::to_string(maxReconnectAttempts_) + " attempts");
                } else {
                    errorCallback_("Reconnection attempt " + std::to_string(reconnectAttempts_) + " failed, retrying...");
                }
            }

            // Schedule next attempt if still enabled
            {
                std::lock_guard<std::mutex> lock(stateMutex_);
                if (autoReconnect_ && !shouldStop_) {
                    // Use a separate thread for the next attempt to avoid stack overflow
                    std::thread([this]() {
                        std::this_thread::sleep_for(std::chrono::milliseconds(500));
                        attemptReconnect();
                    }).detach();
                }
            }
        }
    });
}
